/* ========================================================================
	reporting-shared.css - Shared Reporting App Styles
	======================================================================== */

/* ------------------------------------------------------------------------
	1. Layout & Containers
	------------------------------------------------------------------------ */

/* Main App Container */
.reporting-app {
	display: flex;
	min-height: 100vh;
	background: #fff;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Main Content Area */
.main-content {
	flex: 1;
	padding: 24px 32px;
	overflow-y: auto;
}

/* Grid Layout for Reports */
.reports-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
	gap: 24px;
}

/* ------------------------------------------------------------------------
   2. Header & Section Styles
   ------------------------------------------------------------------------ */

/* Content Header */
.content-header {
	margin-bottom: 24px;
}

.content-header h2 {
	font-size: 28px;
	margin: 0 0 8px 0;
	color: #1a202c;
	font-weight: 600;
}

.content-header p {
	color: #718096;
	margin: 0;
	font-size: 14px;
}

/* Section Header */
.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.section-header h2 {
	font-size: 24px;
	margin: 0;
	color: #1a202c;
	font-weight: 600;
}

.section-description {
	color: #718096;
	margin: 0 0 20px 0;
	font-size: 14px;
}

/* ------------------------------------------------------------------------
   3. Buttons
   ------------------------------------------------------------------------ */

/* Primary Button */
.btn-primary {
	background: #6366f1;
	color: white;
	border: none;
	padding: 12px 24px;
	border-radius: 2px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-primary:hover {
	background: #5855eb;
}

.btn-primary:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* Secondary Button */
.btn-secondary {
	background: #f8f9fa;
	color: #525252;
	border: 1px solid #dee2e6;
	padding: 12px 24px;
	border-radius: 2px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-secondary:hover {
	background: #f5f5f5;
	border-color: #d1d5db;
}

.btn-secondary:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* View All Button */
.view-all-btn {
	background: transparent;
	color: #6366f1;
	border: 1px solid #6366f1;
	padding: 8px 16px;
	border-radius: 2px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
}

.view-all-btn:hover {
	background: #6366f1;
	color: white;
}

.view-all-btn:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* Refresh Button */
.refresh-btn {
	background: transparent;
	color: #718096;
	border: 1px solid #e5e5e5;
	padding: 8px 16px;
	border-radius: 2px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
}

.refresh-btn:hover {
	background: #f8f9fa;
	border-color: #d1d5db;
}

.refresh-btn:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* Generate Button */
.generate-btn {
	background: #6366f1;
	color: white;
	border: none;
	padding: 8px 16px;
	border-radius: 2px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
	flex: 1;
}

.generate-btn:hover {
	background: #5855eb;
}

.generate-btn:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* Favorite Button */
.favorite-btn {
	background: #f8f9fa;
	border: 1px solid #dee2e6;
	color: #6c757d;
	border-radius: 2px;
	padding: 8px;
	cursor: pointer;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}

.favorite-btn svg {
	width: 16px;
	height: 16px;
	fill: none;
	stroke: currentColor;
	stroke-width: 2;
	transition: all 0.2s ease;
}

.favorite-btn:hover {
	background: #f5f5f5;
	border-color: #d1d5db;
}

.favorite-btn.favorited svg {
	fill: #dc3545;
	stroke: #dc3545;
}

.favorite-btn:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* ------------------------------------------------------------------------
   4. Card & Report Card Styles
   ------------------------------------------------------------------------ */

/* Shared Card Styles */
.card {
	background: #fff;
	border: 1px solid #e2e8f0;
	border-radius: 2px;
	padding: 24px;
	transition: all 0.3s ease;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.card:hover {
	border-color: #d1d5db;
}

/* Report Card */
.report-card {
	/* Uses shared .card styles */
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

.report-card-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16px;
}

.report-icon {
	width: 48px;
	height: 48px;
	border-radius: 2px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16px;
	font-size: 20px;
	flex-shrink: 0;
	background: #f7f8fa;
	color: #495057;
	border: 1px solid #dee2e6;
}

.report-content {
	flex: 1;
}

.report-content h3 {
	font-size: 18px;
	margin: 0 0 8px 0;
	color: #1a202c;
	font-weight: 600;
	line-height: 1.3;
}

.report-content p {
	margin: 0 0 16px 0;
	color: #64748b;
	font-size: 14px;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.report-content p.no-description {
	color: #94a3b8;
	font-style: italic;
}

.report-card-footer {
	margin-top: auto;
	padding-top: 4px;
	display: flex;
	gap: 8px;
	align-items: center;
}

/* ------------------------------------------------------------------------
   5. Empty State
   ------------------------------------------------------------------------ */

.empty-state {
	text-align: center;
	padding: 60px 20px;
	color: #718096;
}

.empty-state h3 {
	font-size: 18px;
	margin: 0 0 8px 0;
	color: #4a5568;
}

.empty-state p {
	margin: 0;
	font-size: 14px;
}
/* ------------------------------------------------------------------------
	6. Theme Toggle & Animations
	------------------------------------------------------------------------ */

/* Theme Toggle Button */
.theme-toggle {
	position: fixed;
	top: 20px;
	right: 20px;
	background: #f8f9fa;
	border: 1px solid #dee2e6;
	border-radius: 2px;
	padding: 10px;
	cursor: pointer;
	transition: all 0.2s ease;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 44px;
	height: 44px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-toggle:hover {
	background: #e9ecef;
}

.theme-toggle svg {
	width: 20px;
	height: 20px;
	transition: all 0.2s ease;
}

/* Button Bounce Animation */
@keyframes btnBounce {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(0.992);
	}
	100% {
		transform: scale(1);
	}
}

/* ------------------------------------------------------------------------
	7. Dark Mode Overrides
	------------------------------------------------------------------------ */

/* Dark Mode Theme Toggle */
.reporting-app.dark .theme-toggle {
	background: #4b5563;
	border-color: #6b7280;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
	color: #f9fafb;
}

.reporting-app.dark .theme-toggle:hover {
	background: #6b7280;
	border-color: #9ca3af;
}

/* Dark Mode Overrides for Shared Elements */
.reporting-app.dark {
	background: #1f2937;
}

.reporting-app.dark .main-content {
	background: #1f2937;
}

.reporting-app.dark .content-header h2 {
	color: #f9fafb;
}

.reporting-app.dark .content-header p {
	color: #d1d5db;
}

.reporting-app.dark .btn-primary {
	background: #6366f1;
}

.reporting-app.dark .btn-primary:hover {
	background: #5855eb;
}

.reporting-app.dark .btn-secondary {
	background: #374151;
	color: #f9fafb;
	border: 1px solid #4b5563;
}

.reporting-app.dark .btn-secondary:hover {
	background: #4b5563;
	border-color: #6b7280;
}

.reporting-app.dark .view-all-btn {
	color: #6366f1;
	border: 1px solid #6366f1;
	background: transparent;
}

.reporting-app.dark .view-all-btn:hover {
	background: #6366f1;
	color: white;
}

.reporting-app.dark .refresh-btn {
	color: #d1d5db;
	border: 1px solid #4b5563;
	background: transparent;
}

.reporting-app.dark .refresh-btn:hover {
	background: #374151;
	border-color: #6b7280;
	color: #f9fafb;
}

.reporting-app.dark .generate-btn {
	background: #6366f1;
}

.reporting-app.dark .generate-btn:hover {
	background: #5855eb;
}

.reporting-app.dark .favorite-btn {
	background: #374151;
	border: 1px solid #4b5563;
	color: #d1d5db;
}

.reporting-app.dark .favorite-btn:hover {
	background: #4b5563;
	border-color: #6b7280;
	color: #f9fafb;
}

.reporting-app.dark .favorite-btn.favorited svg {
	fill: #f87171;
	stroke: #f87171;
}

.reporting-app.dark .card {
	background: #374151;
	border: 1px solid #4b5563;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.reporting-app.dark .card:hover {
	border-color: #6b7280;
}

.reporting-app.dark .section-header h2 {
	color: #f9fafb;
}

.reporting-app.dark .section-description {
	color: #d1d5db;
}

.reporting-app.dark .empty-state {
	color: #d1d5db;
}

.reporting-app.dark .empty-state h3 {
	color: #f9fafb;
}

/* Dark Report Cards */
.reporting-app.dark .report-card {
	/* Uses shared .card dark styles */
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.reporting-app.dark .report-icon {
	background: #4b5563;
	color: #f9fafb;
	border: 1px solid #6b7280;
}

.reporting-app.dark .report-content h3 {
	color: #f9fafb;
}

.reporting-app.dark .report-content p {
	color: #d1d5db;
}

.reporting-app.dark .report-content p.no-description {
	color: #9ca3af;
}
